/**
 * Editor pane component with markdown editing capabilities
 */

'use client';

import React, { useRef, useEffect, useState, useCallback, useMemo } from 'react';
import { useApp } from '@/contexts/AppContext';
import { useResponsive } from '@/hooks/useResponsive';
import { debounce } from '@/utils/file';
import { EditorToolbar } from './EditorToolbar';

interface EditorPaneProps {
  // Props removed - using react-scroll-sync instead
}

export function EditorPane({}: EditorPaneProps = {}) {
  const { state, updateContent, dispatch } = useApp();
  const responsive = useResponsive();
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [localContent, setLocalContent] = useState(state.editor.content);
  const [undoStack, setUndoStack] = useState<string[]>([]);
  const [redoStack, setRedoStack] = useState<string[]>([]);

  // Sync local content with state when file changes
  useEffect(() => {
    setLocalContent(state.editor.content);
    setUndoStack([]);
    setRedoStack([]);
  }, [state.editor.currentFile?.id]);

  // Debounced content update to improve performance
  const debouncedUpdateContent = useCallback(
    debounce((content: string) => {
      updateContent(content);
    }, 150), // Reduced debounce time for better responsiveness
    [updateContent]
  );

  // Add to undo stack
  const addToUndoStack = useCallback((content: string) => {
    setUndoStack(prev => {
      const newStack = [...prev, content];
      return newStack.slice(-50); // Keep only last 50 states
    });
    setRedoStack([]); // Clear redo stack when new content is added
  }, []);

  // Handle content changes with immediate local update
  const handleContentChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const content = event.target.value;

    // Add current content to undo stack before changing
    if (localContent !== content && localContent.length > 0) {
      addToUndoStack(localContent);
    }

    setLocalContent(content); // Immediate local update for responsiveness
    debouncedUpdateContent(content);
    updateCursorPosition();
  };

  // Undo functionality
  const handleUndo = useCallback(() => {
    if (undoStack.length === 0) return;

    const previousContent = undoStack[undoStack.length - 1];
    setRedoStack(prev => [localContent, ...prev]);
    setUndoStack(prev => prev.slice(0, -1));
    setLocalContent(previousContent);
    updateContent(previousContent);

    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [undoStack, localContent, updateContent]);

  // Redo functionality
  const handleRedo = useCallback(() => {
    if (redoStack.length === 0) return;

    const nextContent = redoStack[0];
    setUndoStack(prev => [...prev, localContent]);
    setRedoStack(prev => prev.slice(1));
    setLocalContent(nextContent);
    updateContent(nextContent);

    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [redoStack, localContent, updateContent]);

  // Clear editor
  const handleClear = useCallback(() => {
    if (localContent.length > 0) {
      addToUndoStack(localContent);
    }
    setLocalContent('');
    updateContent('');

    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [localContent, updateContent, addToUndoStack]);

  // Paste functionality
  const handlePaste = useCallback(async () => {
    try {
      const text = await navigator.clipboard.readText();
      if (textareaRef.current) {
        const textarea = textareaRef.current;
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const newContent = localContent.substring(0, start) + text + localContent.substring(end);

        addToUndoStack(localContent);
        setLocalContent(newContent);
        updateContent(newContent);

        // Set cursor position after pasted text
        setTimeout(() => {
          textarea.setSelectionRange(start + text.length, start + text.length);
          textarea.focus();
        }, 0);
      }
    } catch (error) {
      console.error('Failed to paste from clipboard:', error);
    }
  }, [localContent, updateContent, addToUndoStack]);

  // Update cursor position
  const updateCursorPosition = () => {
    if (!textareaRef.current) return;

    const textarea = textareaRef.current;
    const content = textarea.value;
    const cursorPos = textarea.selectionStart;

    const lines = content.substring(0, cursorPos).split('\n');
    const line = lines.length;
    const column = lines[lines.length - 1].length + 1;

    dispatch({
      type: 'EDITOR_ACTION',
      payload: {
        type: 'SET_CURSOR_POSITION',
        payload: { line, column }
      }
    });
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case 's':
          event.preventDefault();
          // Save functionality is handled by the header
          break;
        case 'b':
          event.preventDefault();
          insertMarkdown('**', '**', 'bold text');
          break;
        case 'i':
          event.preventDefault();
          insertMarkdown('*', '*', 'italic text');
          break;
        case 'k':
          event.preventDefault();
          insertMarkdown('[', '](url)', 'link text');
          break;
        case 'z':
          event.preventDefault();
          if (event.shiftKey) {
            handleRedo();
          } else {
            handleUndo();
          }
          break;
        case 'y':
          event.preventDefault();
          handleRedo();
          break;
        case 'v':
          if (event.shiftKey) {
            event.preventDefault();
            handlePaste();
          }
          break;
      }
    }

    // Tab handling for indentation
    if (event.key === 'Tab') {
      event.preventDefault();
      const textarea = event.currentTarget;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const value = textarea.value;

      if (event.shiftKey) {
        // Unindent
        const lineStart = value.lastIndexOf('\n', start - 1) + 1;
        const lineText = value.substring(lineStart, start);
        if (lineText.startsWith('  ')) {
          const newValue = value.substring(0, lineStart) +
                          lineText.substring(2) +
                          value.substring(start);
          textarea.value = newValue;
          textarea.setSelectionRange(start - 2, end - 2);
          debouncedUpdateContent(newValue);
        }
      } else {
        // Indent
        const newValue = value.substring(0, start) + '  ' + value.substring(end);
        textarea.value = newValue;
        textarea.setSelectionRange(start + 2, end + 2);
        debouncedUpdateContent(newValue);
      }
    }
  };

  // Insert markdown formatting
  const insertMarkdown = (before: string, after: string, placeholder: string) => {
    if (!textareaRef.current) return;

    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = localContent.substring(start, end);
    const replacement = selectedText || placeholder;

    // Add current content to undo stack
    addToUndoStack(localContent);

    const newValue =
      localContent.substring(0, start) +
      before + replacement + after +
      localContent.substring(end);

    // Update both local and global state
    setLocalContent(newValue);
    updateContent(newValue);

    // Set cursor position after state update
    setTimeout(() => {
      if (selectedText) {
        textarea.setSelectionRange(start + before.length, start + before.length + replacement.length);
      } else {
        textarea.setSelectionRange(start + before.length, start + before.length + placeholder.length);
      }
      textarea.focus();
    }, 0);
  };

  // Handle drag and drop
  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);

    const files = Array.from(event.dataTransfer.files);
    const imageFiles = files.filter(file => file.type.startsWith('image/'));

    if (imageFiles.length > 0) {
      imageFiles.forEach(file => {
        const reader = new FileReader();
        reader.onload = (e) => {
          const dataUrl = e.target?.result as string;
          const markdown = `![${file.name}](${dataUrl})`;
          insertMarkdown('', '', markdown);
        };
        reader.readAsDataURL(file);
      });
    }
  };

  // Focus textarea when file is opened
  useEffect(() => {
    if (state.editor.currentFile && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [state.editor.currentFile]);

  // Update cursor position on click
  const handleClick = () => {
    setTimeout(updateCursorPosition, 0);
  };

  // Scroll synchronization now handled by react-scroll-sync

  // Memoized line numbers for performance
  const lineNumbers = useMemo(() => {
    const lines = localContent.split('\n');
    return lines.map((_, index) => index + 1);
  }, [localContent]);

  // Calculate line height and padding for line numbers alignment
  const lineHeight = state.settings.fontSize * 1.6;
  const paddingTop = 16; // 4 * 4px (p-4)

  return (
    <div className="h-full flex flex-col bg-white dark:bg-gray-900">
      {/* Toolbar */}
      <EditorToolbar
        onInsertMarkdown={insertMarkdown}
        onUndo={handleUndo}
        onRedo={handleRedo}
        onClear={handleClear}
        onPaste={handlePaste}
        canUndo={undoStack.length > 0}
        canRedo={redoStack.length > 0}
      />

      {/* Editor area */}
      <div className="flex-1 relative">
        {state.editor.currentFile ? (
          <div
            className={`h-full relative ${isDragging ? 'bg-blue-50 dark:bg-blue-900/20' : ''}`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <textarea
              ref={textareaRef}
              value={localContent}
              onChange={handleContentChange}
              onKeyDown={handleKeyDown}
              onClick={handleClick}
              onKeyUp={updateCursorPosition}


              className={`w-full h-full resize-none border-none outline-none bg-transparent text-gray-900 dark:text-white font-mono whitespace-pre-wrap ${
                state.settings.lineNumbers && !responsive.isMobile ? 'pl-16' : responsive.isMobile ? 'p-2' : 'p-4'
              }`}
              style={{
                fontSize: responsive.isMobile ? '16px' : Math.min(state.settings.fontSize, 20) + 'px',
                lineHeight: responsive.isMobile ? '24px' : Math.min(lineHeight, 32) + 'px',
                tabSize: 2,
                maxWidth: '100%',
                overflowWrap: 'break-word',
                paddingTop: responsive.isMobile ? '8px' : `${paddingTop}px`,
                paddingRight: responsive.isMobile ? '8px' : '16px',
                paddingBottom: responsive.isMobile ? '8px' : '16px'
              }}
              placeholder="Start writing your markdown here..."
              spellCheck={false}
            />

            {/* Line numbers */}
            {state.settings.lineNumbers && !responsive.isMobile && (
              <div
                className="absolute left-0 top-0 w-12 bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 pointer-events-none select-none overflow-hidden"
                style={{
                  paddingTop: `${paddingTop}px`,
                }}
              >
                <div
                  className="text-gray-400 dark:text-gray-600 font-mono text-right pr-2"
                  style={{
                    fontSize: `${state.settings.fontSize}px`,
                    lineHeight: `${lineHeight}px`
                  }}
                >
                  {lineNumbers.map((lineNum) => (
                    <div key={lineNum} style={{ height: `${lineHeight}px` }}>
                      {lineNum}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Drag overlay */}
            {isDragging && (
              <div className="absolute inset-0 bg-blue-100 dark:bg-blue-900/30 border-2 border-dashed border-blue-400 flex items-center justify-center">
                <div className="text-center">
                  <svg className="w-12 h-12 text-blue-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                  <p className="text-blue-600 font-medium">Drop images here to insert</p>
                </div>
              </div>
            )}

            {/* Loading overlay */}
            {state.editor.isLoading && (
              <div className="absolute inset-0 bg-white/80 dark:bg-gray-900/80 flex items-center justify-center">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                  <p className="text-gray-600 dark:text-gray-400">Loading...</p>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="h-full flex items-center justify-center text-gray-500 dark:text-gray-400">
            <div className="text-center">
              <svg className="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="text-lg font-medium mb-2">No file selected</h3>
              <p className="text-sm">Create a new file or select an existing one to start editing</p>
            </div>
          </div>
        )}

        {/* Error display */}
        {state.editor.error && (
          <div className="absolute bottom-4 right-4 bg-red-100 dark:bg-red-900/50 border border-red-300 dark:border-red-700 rounded-md p-3 max-w-sm">
            <div className="flex items-center space-x-2">
              <svg className="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-sm text-red-800 dark:text-red-200">{state.editor.error}</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
